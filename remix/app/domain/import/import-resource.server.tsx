import { Args } from "~/server/resource/resource-helpers.server";
import { isEditorQb, userSessionId } from "~/domain/member/member-queries.server";
import { ResourceError } from "~/utils/error";
import { nowValue } from "~/kysely/kysely-helpers";

export const importResource: Args<"import"> = {
  authorize: (args) => isEditorQb(args).executeTakeFirst(),
  beforeMutate: async (args) => {
    const name = args.data?.name;
    if (!name) throw new ResourceError("Import name is required");
    const rowsStr = args.data?.rows;
    if (!rowsStr) throw new ResourceError("Import rows are required");
    const rows = JSON.parse(rowsStr);
    if (!(rows instanceof Array)) throw new ResourceError("rows should be an array");

    const isEditor = await isEditorQb(args).executeTakeFirst();

    if (!isEditor) throw new ResourceError("Unauthorized");

    const importObj = await args.trx
      .insertInto("import")
      .values({
        name: name,
        created_at: nowValue,
        created_by_user_session_id: userSessionId(args),
        rows: rowsStr,
      })
      .returningAll()
      .executeTakeFirstOrThrow();

    await args.trx
      .insertInto("import_row")
      .values(
        rows.map((row) => ({
          import_id: importObj.id,
          data: row,
        })),
      )
      .execute();

    return {
      id: importObj.id,
      operation: "ignore",
      data: args.data,
    };
  },
  insert: (args) => false,
  update: (args) => false,
  delete: () => true,
};